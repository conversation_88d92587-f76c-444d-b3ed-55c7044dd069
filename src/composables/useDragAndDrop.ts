import { ref, computed, readonly } from "vue";

/**
 * 拖拽文件信息接口
 */
export interface DragDropFile {
  /** 文件对象 */
  file: File;
  /** 文件路径（Electron 原生路径或相对路径） */
  path?: string;
  /** 相对路径（用于保持目录结构） */
  relativePath?: string;
  /** 是否来自目录 */
  isFromDirectory?: boolean;
}

/**
 * 拖拽处理选项
 */
export interface DragDropOptions {
  /** 是否允许目录拖拽 */
  allowDirectories?: boolean;
  /** 最大文件数量限制 */
  maxFiles?: number;
  /** 最大文件大小限制（字节） */
  maxSize?: number;
  /** 接受的文件类型 */
  accept?: string;
  /** 是否使用 Electron 原生 API */
  useElectronAPI?: boolean;
}

/**
 * 拖拽处理结果
 */
export interface DragDropResult {
  /** 处理成功的文件列表 */
  files: DragDropFile[];
  /** 错误信息列表 */
  errors: string[];
  /** 是否来自 Electron 原生拖拽 */
  isElectronDrop?: boolean;
}

/**
 * 拖拽处理回调
 */
export interface DragDropCallbacks {
  /** 拖拽开始回调 */
  onDragStart?: () => void;
  /** 拖拽结束回调 */
  onDragEnd?: () => void;
  /** 文件处理完成回调 */
  onFilesProcessed?: (result: DragDropResult) => void;
  /** 错误处理回调 */
  onError?: (errors: string[]) => void;
  /** 智能打包自动触发回调 */
  onSmartPackTriggered?: (files: File[], fileCount: number) => Promise<void>;
}

/**
 * 统一拖拽处理 Composable
 *
 * 功能特性：
 * 1. 统一的拖拽事件处理
 * 2. 支持 Electron 原生 API 和前端 API
 * 3. 智能文件处理和验证
 * 4. 完善的错误处理机制
 * 5. 目录递归处理
 */
export function useDragAndDrop(options: DragDropOptions = {}, callbacks: DragDropCallbacks = {}) {
  const {
    allowDirectories = true,
    maxFiles = 1000,
    maxSize = 100 * 1024 * 1024, // 100MB
    accept = "",
    useElectronAPI = true,
  } = options;

  const { onDragStart, onDragEnd, onFilesProcessed, onError, onSmartPackTriggered } = callbacks;

  // 拖拽状态
  const isDragging = ref(false);
  const dragCounter = ref(0);

  // 检查是否在 Electron 环境
  const isElectron = computed(() => {
    return typeof window !== "undefined" && window.electronAPI && useElectronAPI;
  });

  /**
   * 验证文件
   */
  const validateFile = (file: File): string | null => {
    // 检查文件大小
    if (file.size > maxSize) {
      return `文件 "${file.name}" 大小超过限制 (${formatFileSize(maxSize)})`;
    }

    // 检查文件类型
    if (accept && !isFileTypeAccepted(file, accept)) {
      return `文件 "${file.name}" 类型不被接受`;
    }

    return null;
  };

  /**
   * 检查文件类型是否被接受
   */
  const isFileTypeAccepted = (file: File, accept: string): boolean => {
    if (!accept) return true;

    const acceptTypes = accept.split(",").map((type) => type.trim());

    return acceptTypes.some((acceptType) => {
      if (acceptType.startsWith(".")) {
        // 扩展名匹配
        return file.name.toLowerCase().endsWith(acceptType.toLowerCase());
      } else if (acceptType.includes("/*")) {
        // MIME 类型通配符匹配
        const [mainType] = acceptType.split("/");
        return file.type.startsWith(mainType + "/");
      } else {
        // 精确 MIME 类型匹配
        return file.type === acceptType;
      }
    });
  };

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  /**
   * 处理拖拽进入事件
   */
  const handleDragEnter = (event: DragEvent) => {
    event.preventDefault();
    event.stopPropagation();

    dragCounter.value++;
    console.log(`🎯 拖拽进入: dragCounter = ${dragCounter.value}`);

    if (dragCounter.value === 1) {
      isDragging.value = true;
      console.log(`🎯 开始拖拽状态: isDragging = true`);
      onDragStart?.();
    }
  };

  /**
   * 处理拖拽悬停事件
   */
  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    event.stopPropagation();

    // 设置拖拽效果
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = "copy";
    }
  };

  /**
   * 处理拖拽离开事件
   */
  const handleDragLeave = (event: DragEvent) => {
    event.preventDefault();
    event.stopPropagation();

    dragCounter.value--;
    console.log(`🎯 拖拽离开: dragCounter = ${dragCounter.value}`);

    if (dragCounter.value === 0) {
      isDragging.value = false;
      console.log(`🎯 结束拖拽状态: isDragging = false`);
      onDragEnd?.();
    }
  };

  /**
   * 处理拖拽放置事件
   */
  const handleDrop = async (event: DragEvent): Promise<DragDropResult> => {
    event.preventDefault();
    event.stopPropagation();

    console.log(`🎯 文件放置事件触发`);

    // 重置拖拽状态
    isDragging.value = false;
    dragCounter.value = 0;
    onDragEnd?.();

    try {
      let result: DragDropResult;

      if (isElectron.value && typeof window.electronAPI?.dragDrop?.handleFileDrop === "function") {
        console.log(`🎯 使用 Electron 原生 API 处理拖拽`);
        result = await processElectronDrop(event);
      } else {
        console.log(`🎯 使用前端 API 处理拖拽`);
        result = await processFrontendDrop(event);
      }

      // 检查文件数量限制
      if (result.files.length > maxFiles) {
        result.errors.push(`文件数量超过限制 (${maxFiles})`);
        result.files = result.files.slice(0, maxFiles);
      }

      console.log(`🎯 拖拽处理完成: ${result.files.length} 个文件, ${result.errors.length} 个错误`);

      // 智能打包自动触发
      const shouldAutoSmartPack = result.files.length >= 50;
      if (shouldAutoSmartPack && onSmartPackTriggered) {
        console.log(`🎯 触发智能打包: ${result.files.length} 个文件`);
        // 转换为 File[] 格式
        const files = result.files.map((f) => f.file);
        await onSmartPackTriggered(files, result.files.length);
      } else {
        console.log(`🎯 使用常规处理: ${result.files.length} 个文件`);
        console.log(`🎯 onFilesProcessed 回调存在:`, !!onFilesProcessed);
        // 文件数量未达到阈值或没有智能打包回调，使用常规处理
        if (onFilesProcessed) {
          console.log(`🎯 调用 onFilesProcessed 回调，传递结果:`, result);
          onFilesProcessed(result);
        } else {
          console.warn(`🎯 onFilesProcessed 回调不存在，无法处理文件`);
        }
      }

      if (result.errors.length > 0) {
        onError?.(result.errors);
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const result: DragDropResult = {
        files: [],
        errors: [errorMessage],
      };

      onError?.(result.errors);
      return result;
    }
  };

  /**
   * 使用 Electron 原生 API 处理拖拽
   */
  const processElectronDrop = async (event: DragEvent): Promise<DragDropResult> => {
    const files: DragDropFile[] = [];
    const errors: string[] = [];

    try {
      console.log(`🎯 processElectronDrop: 开始处理 Electron 拖拽事件`);

      // 获取拖拽的文件路径
      const dataTransfer = event.dataTransfer;
      if (!dataTransfer?.files) {
        throw new Error("无法获取拖拽文件");
      }

      const filePaths = Array.from(dataTransfer.files)
        .map((file) => (file as any).path)
        .filter(Boolean);

      console.log(`🎯 processElectronDrop: 提取到文件路径 ${filePaths.length} 个:`, filePaths);

      if (filePaths.length === 0) {
        throw new Error("未检测到有效的文件路径");
      }

      // 调用 Electron 主进程处理文件
      console.log(`🎯 processElectronDrop: 调用 Electron API 处理文件`);
      const response = await window.electronAPI?.dragDrop?.handleFileDrop(filePaths);

      console.log(`🎯 processElectronDrop: Electron API 响应:`, response);

      if (!response.success) {
        throw new Error(response.error || "处理拖拽文件失败");
      }

      // 转换为 DragDropFile 格式
      if (response.data?.files) {
        console.log(`🎯 processElectronDrop: 开始处理 ${response.data.files.length} 个文件`);

        for (const fileInfo of response.data.files) {
          try {
            console.log(`🎯 processElectronDrop: 处理文件 "${fileInfo.name}" (${fileInfo.path})`);

            // 创建 File 对象（这里需要从路径读取文件）
            const file = await createFileFromPath(fileInfo.path);
            console.log(`🎯 processElectronDrop: 成功创建 File 对象:`, file.name, file.size);

            const dragDropFile: DragDropFile = {
              file,
              path: fileInfo.path,
              relativePath: fileInfo.relativePath,
              isFromDirectory: fileInfo.relativePath !== fileInfo.name,
            };

            // 验证文件
            const error = validateFile(file);
            if (error) {
              console.log(`🎯 processElectronDrop: 文件验证失败: ${error}`);
              errors.push(error);
              continue;
            }

            files.push(dragDropFile);
            console.log(`🎯 processElectronDrop: 文件添加成功，当前文件数: ${files.length}`);
          } catch (error) {
            console.error(`🎯 processElectronDrop: 处理文件失败:`, error);
            errors.push(`处理文件 "${fileInfo.name}" 失败: ${error}`);
          }
        }
      } else {
        console.warn(`🎯 processElectronDrop: 响应中没有文件数据`);
      }

      return {
        files,
        errors,
        isElectronDrop: true,
      };
    } catch (error) {
      return {
        files: [],
        errors: [error instanceof Error ? error.message : String(error)],
        isElectronDrop: true,
      };
    }
  };

  /**
   * 从文件路径创建 File 对象
   */
  const createFileFromPath = async (filePath: string): Promise<File> => {
    console.log(`🎯 createFileFromPath: 开始创建文件对象，路径: ${filePath}`);

    if (!isElectron.value) {
      throw new Error("createFileFromPath 只能在 Electron 环境中使用");
    }

    try {
      // 获取文件信息
      console.log(`🎯 createFileFromPath: 调用 getFileInfo API`);
      const response = await window.electronAPI?.getFileInfo(filePath);

      console.log(`🎯 createFileFromPath: getFileInfo 响应:`, response);

      if (!response?.success || !response.data) {
        throw new Error(response?.error || "获取文件信息失败");
      }

      const fileInfo = response.data;
      console.log(`🎯 createFileFromPath: 文件信息:`, fileInfo);

      // 创建一个特殊的 File 对象，包含路径信息
      const fileName = filePath.split("/").pop() || filePath.split("\\").pop() || "unknown";

      // 创建一个虚拟的 File 对象，不实际读取文件内容
      // 在 Electron 环境中，我们主要使用文件路径进行上传
      const file = new File([], fileName, {
        type: "application/octet-stream",
        lastModified: fileInfo.mtime || Date.now(),
      });

      // 添加路径信息到 File 对象
      Object.defineProperty(file, "path", {
        value: filePath,
        writable: false,
      });

      Object.defineProperty(file, "size", {
        value: fileInfo.size || 0,
        writable: false,
      });

      return file;
    } catch (error) {
      throw new Error(`无法创建文件对象: ${error}`);
    }
  };

  /**
   * 使用前端 API 处理拖拽
   */
  const processFrontendDrop = async (event: DragEvent): Promise<DragDropResult> => {
    const files: DragDropFile[] = [];
    const errors: string[] = [];

    console.log(`🎯 开始前端拖拽处理`);

    const dataTransfer = event.dataTransfer;
    if (!dataTransfer) {
      throw new Error("无法获取拖拽数据");
    }

    console.log(`🎯 DataTransfer 对象:`, {
      items: dataTransfer.items?.length,
      files: dataTransfer.files?.length,
      types: dataTransfer.types,
    });

    // 处理 DataTransferItemList
    if (dataTransfer.items) {
      const items = Array.from(dataTransfer.items);

      for (const item of items) {
        if (item.kind !== "file") continue;

        const entry = item.webkitGetAsEntry();
        if (!entry) continue;

        try {
          if (entry.isFile) {
            const file = item.getAsFile();
            if (file) {
              const dragDropFile: DragDropFile = {
                file,
                relativePath: file.name,
                isFromDirectory: false,
              };

              const error = validateFile(file);
              if (error) {
                errors.push(error);
                continue;
              }

              files.push(dragDropFile);
            }
          } else if (entry.isDirectory && allowDirectories) {
            const dirFiles = await processDirectoryEntry(entry as FileSystemDirectoryEntry, entry.name);
            files.push(...dirFiles);
          }
        } catch (error) {
          errors.push(`处理项目 "${entry.name}" 失败: ${error}`);
        }
      }
    } else if (dataTransfer.files) {
      // 降级处理：直接使用 files
      const fileList = Array.from(dataTransfer.files);

      for (const file of fileList) {
        const dragDropFile: DragDropFile = {
          file,
          relativePath: file.name,
          isFromDirectory: false,
        };

        const error = validateFile(file);
        if (error) {
          errors.push(error);
          continue;
        }

        files.push(dragDropFile);
      }
    }

    return {
      files,
      errors,
      isElectronDrop: false,
    };
  };

  /**
   * 递归处理目录条目
   */
  const processDirectoryEntry = async (directoryEntry: FileSystemDirectoryEntry, basePath: string): Promise<DragDropFile[]> => {
    const files: DragDropFile[] = [];

    return new Promise((resolve) => {
      const reader = directoryEntry.createReader();

      const readEntries = () => {
        reader.readEntries(async (entries) => {
          if (entries.length === 0) {
            resolve(files);
            return;
          }

          const promises = entries.map(async (entry) => {
            if (entry.isFile) {
              const fileEntry = entry as FileSystemFileEntry;
              return new Promise<void>((resolveFile) => {
                fileEntry.file((file) => {
                  const relativePath = `${basePath}/${file.name}`;

                  // 设置 webkitRelativePath 属性
                  Object.defineProperty(file, "webkitRelativePath", {
                    value: relativePath,
                    writable: false,
                  });

                  const dragDropFile: DragDropFile = {
                    file,
                    relativePath,
                    isFromDirectory: true,
                  };

                  const error = validateFile(file);
                  if (!error) {
                    files.push(dragDropFile);
                  }

                  resolveFile();
                });
              });
            } else if (entry.isDirectory) {
              const subFiles = await processDirectoryEntry(entry as FileSystemDirectoryEntry, `${basePath}/${entry.name}`);
              files.push(...subFiles);
            }
          });

          await Promise.all(promises);
          readEntries(); // 继续读取更多条目
        });
      };

      readEntries();
    });
  };

  return {
    // 状态
    isDragging: readonly(isDragging),
    isElectron,

    // 事件处理函数
    handleDragEnter,
    handleDragOver,
    handleDragLeave,
    handleDrop,

    // 工具函数
    validateFile,
    formatFileSize,
    isFileTypeAccepted,
  };
}

// 导出类型已在上面定义，无需重复导出
