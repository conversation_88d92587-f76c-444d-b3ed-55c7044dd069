<template>
  <div class="flex flex-col h-full">
    <!-- 固定头部区域 -->
    <div class="flex-shrink-0">
      <!-- 面包屑导航 -->
      <Breadcrumb class="px-2 pb-2 border-b" :breadcrumbs="breadcrumbs" :loading="isLoading" />

      <!-- 筛选器组件 -->
      <FilterList :filters="filterOptions" :loading="apiFiltersLoading" @filter-change="handleFilterChange" />

      <!-- 工具栏 -->
      <ToolsBar :file-count="directoryData.total" :current-page="currentPage" :page-size="pageSize"
        :total="directoryData.total" :loading="isLoading" @search="handleSearch" @refresh="handleRefresh"
        @create-folder="handleCreateFolder" @upload="handleUpload" @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange" />
    </div>

    <!-- 可滚动的文件显示区域 -->
    <div class="p-2" style="height: calc(100vh - 256px)">
      <!-- 加载状态覆盖层 -->
      <div v-if="isLoading && !directoryData.items.length" class="flex justify-center items-center h-full">
        <div class="flex flex-col items-center space-y-4">
          <div class="w-8 h-8 rounded-full border-b-2 animate-spin border-primary"></div>
          <p class="text-sm text-muted-foreground">正在加载目录内容...</p>
        </div>
      </div>

      <!-- 文件显示区域 -->
      <FilesView ref="filesViewRef" :items="displayItems" :view-mode="viewMode" :empty-icon="folderConfig.icon"
        :empty-title="emptyTitle" :empty-message="folderConfig.emptyMessage"
        :upload-button-text="`上传${folderConfig.fileType}`" :api-filter-options="apiFilterOptions" :loading="isLoading"
        :page-size="pageSize" :is-downloading="isDownloading" @file-click="handleFileClick"
        @file-double-click="handleFileDoubleClick" @folder-click="handleFolderClick" @upload="handleUpload"
        @drag-upload="handleDragUpload" @item-renamed="handleItemRenamed" @item-action="handleFileAction"
        @batch-action="handleBatchAction" @refresh-directory="handleRefreshDirectory" />
    </div>

    <!-- 文件上传弹窗 -->
    <UploadDialog v-model:open="uploadDialogOpen" :title="uploadConfig.title" :description="uploadConfig.description"
      :attribute-selectors="uploadConfig.attributeSelectors" :accept="uploadConfig.accept"
      :max-files="uploadConfig.maxFiles" :max-size="uploadConfig.maxSize" confirm-text="开始上传"
      @confirm="handleUploadConfirm" @cancel="handleUploadCancel" @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError" @files-uploaded="handleFilesUploaded" />

    <!-- 文件预览弹窗 -->
    <FilePreviewModal :visible="showPreview" :preview-url="currentPreviewFile?.preview || ''"
      :file-name="currentPreviewFile?.name" :file-type="currentPreviewFile?.mimeType" @close="closePreview" />

    <!-- 下载路径选择对话框 -->
    <DownloadPathDialog :open="showPathDialog" :download-info="downloadInfo || undefined" @confirm="handlePathConfirm"
      @cancel="handlePathCancel" @update:open="(value: boolean) => showPathDialog = value" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { useFolderConfig } from '@/composables/useFolderConfig'
import { useViewMode } from '@/composables/useViewMode'
import { useDirectoryStructure, type DirectoryItem, type FolderItem, type FileItem, type ApiPathData, type FilterParams } from '@/composables/useDirectoryStructure'
import { useSidebarStore } from '@/store/sidebar'
import api from '@/api'
import FilterList from './FilterList.vue'
import ToolsBar from './ToolsBar/index.vue'
import FilesView from './FilesView/index.vue'
import type { ItemType } from '@/types/files'
import Breadcrumb from '@/components/ui/breadcrumb/Breadcrumb.vue'
import { UploadDialog, type AttributeSelector, type UploadData } from '@/components/Upload'
import { useFileDownload } from '@/composables/useFileDownload'
import { FilePreviewModal, isSupportedFileType } from '@/components/Preview'
import DownloadPathDialog from '@/components/DownloadPathDialog.vue'
import { toast } from 'vue-sonner'


// 使用统一的配置和目录结构
const { getFolderConfig } = useFolderConfig()
const { viewMode } = useViewMode()
const { getDirectoryContents, getBreadcrumbs, loading } = useDirectoryStructure()
const sidebarStore = useSidebarStore()

// 路由和状态
const route = useRoute()
const router = useRouter()
const currentFilters = ref<Record<string, any>>({})
const searchQuery = ref('')
const uploadDialogOpen = ref(false)

// 文件预览状态
const showPreview = ref(false)
const currentPreviewFile = ref<ItemType | null>(null)

// API 筛选项类型定义
interface ApiFilterOption {
  value: number | string
  name: string
}

interface ApiFilterGroup {
  field_name: string
  label: string
  type: 'select' | 'multiselect'
  upload_type: 'select' | 'multiselect' // 用于上传和编辑功能
  source: string
  options: ApiFilterOption[]
  is_form: string  // 是否在表单中显示 "Y" 或 "N"
  is_search: string  // 是否在搜索中显示 "Y" 或 "N"
}

// 筛选项状态
const apiFilterOptions = ref<ApiFilterGroup[]>([])
const apiFiltersLoading = ref(false)
const lastLoadedCategoryId = ref<number | null>(null)

// 分页参数
const currentPage = ref(1)
const pageSize = ref(20)

// 目录内容加载状态锁
const isLoadingContents = ref(false)
// 组件初始化标记
const isInitialized = ref(false)

// 综合的加载状态 - 包含所有可能的加载场景
const isLoading = computed(() => {
  return loading.value || isLoadingContents.value || apiFiltersLoading.value || sidebarStore.isLoading
})

// 当前目录数据
const directoryData = ref<{
  items: DirectoryItem[];
  total: number;
  pathData: ApiPathData[];
  pathDataStr: string;
  categoryId: string;
  categoryKey: string;
}>({
  items: [],
  total: 0,
  pathData: [],
  pathDataStr: '',
  categoryId: '',
  categoryKey: '',
})

// 获取当前路径信息
const folderType = computed(() => route.params.folderType as string)
const categoryId = computed(() => {
  const queryId = route.query.categoryId as string
  return queryId ? Number(queryId) : null
})
const currentPath = computed(() => {
  const pathMatch = route.params.pathMatch as string[] | string
  if (Array.isArray(pathMatch)) {
    return pathMatch.join('/')
  }
  return pathMatch || ''
})

// 构建筛选参数
const buildFilterParams = (): FilterParams => {
  const filters: FilterParams & Record<string, any> = {}

  // 添加搜索查询
  if (searchQuery.value.trim()) {
    filters.search = searchQuery.value.trim()
  }

  // 添加动态筛选字段
  Object.keys(currentFilters.value).forEach(fieldName => {
    const fieldValue = currentFilters.value[fieldName]

    // 跳过空值
    if (fieldValue === null || fieldValue === undefined) {
      return
    }

    // 处理数组类型的多选筛选
    if (Array.isArray(fieldValue) && fieldValue.length > 0) {
      filters[fieldName] = fieldValue
    }

    // 处理单选筛选
    if (!Array.isArray(fieldValue) && fieldValue !== null && fieldValue !== undefined) {
      filters[fieldName] = fieldValue
    }
  })

  return filters
}

// 获取当前目录的 parentId
const getCurrentParentId = (): number | string => {
  // 首先检查路由查询参数中是否有 parentId（点击文件夹时传递的）
  const routeParentId = route.query.parentId as string
  if (routeParentId) {
    return Number(routeParentId)
  }

  // 如果没有路径，说明是一级目录，parentId 是该分类自己的 id
  if (!currentPath.value) {
    // 从 sidebar store 中获取当前分类的 id
    const currentCategoryId = categoryId.value
    if (currentCategoryId) {
      const categoryInfo = sidebarStore.sidebarItems.find(item => item.category_id === currentCategoryId)
      if (categoryInfo) {
        return categoryInfo.id
      }
    }
    // 如果找不到分类信息，返回 categoryId 本身
    return currentCategoryId || 0
  }

  // 对于有路径但没有 parentId 查询参数的情况（可能是直接输入 URL）
  // 暂时返回分类 id 作为默认值
  const currentCategoryId = categoryId.value
  if (currentCategoryId) {
    const categoryInfo = sidebarStore.sidebarItems.find(item => item.category_id === currentCategoryId)
    if (categoryInfo) {
      return categoryInfo.id
    }
  }


  return currentCategoryId || 0
}

// 加载筛选选项
const loadFilterOptions = async (categoryId: number, forceReload = false) => {
  // 如果正在加载或者已经加载过相同的分类（且不强制重新加载），跳过
  if (apiFiltersLoading.value || (lastLoadedCategoryId.value === categoryId && !forceReload)) {
    return
  }

  apiFiltersLoading.value = true
  try {
    const response = await api.common.getAllFilterOptions(categoryId)
    if (response.code === 0 && response.data && response.data.form_items) {
      apiFilterOptions.value = response.data.form_items
      lastLoadedCategoryId.value = categoryId

    } else {
      apiFilterOptions.value = []
      lastLoadedCategoryId.value = null
    }
  } catch (error) {
    apiFilterOptions.value = []
    lastLoadedCategoryId.value = null
  } finally {
    apiFiltersLoading.value = false
  }
}

// 加载目录内容
const loadDirectoryContents = async (reason?: string) => {
  // 防止重复调用
  if (isLoadingContents.value) {
    return
  }

  const currentCategoryId = categoryId.value
  if (!currentCategoryId) {
    return
  }

  isLoadingContents.value = true

  try {
    // 确保 sidebar 数据已加载（只在未加载时才调用）
    if (!sidebarStore.isLoaded && !sidebarStore.isLoading) {
      await sidebarStore.fetchSidebarData()
    } else if (sidebarStore.isLoading) {
      // 如果正在加载中，等待加载完成
      while (sidebarStore.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 50))
      }
    }

    // 加载筛选选项
    await loadFilterOptions(currentCategoryId)

    // 构建筛选参数
    const filters = buildFilterParams()

    // 动态确定 parentId
    const parentId = getCurrentParentId()

    const result = await getDirectoryContents(
      currentCategoryId,
      parentId,
      currentPage.value,
      pageSize.value,
      filters
    )
    directoryData.value = result
    console.log(`目录内容加载完成 (原因: ${reason || '未知'})`)

    // 标记为已初始化
    if (!isInitialized.value) {
      isInitialized.value = true
    }
  } catch (err) {
    console.error('加载目录内容失败:', err)
  } finally {
    isLoadingContents.value = false
  }
}

// 面包屑导航
const breadcrumbs = computed(() => {
  return getBreadcrumbs(
    directoryData.value.pathData,
    directoryData.value.categoryKey,
    directoryData.value.categoryId
  )
})

// 获取当前文件夹配置
const folderConfig = computed(() => {
  const baseConfig = getFolderConfig(folderType.value)
  return {
    ...baseConfig,
    title: baseConfig.name
  }
})

// 筛选器配置 - 转换 API 数据为 FilterList 期待的格式
const filterOptions = computed(() => {
  return apiFilterOptions.value.map(apiFilter => ({
    field_name: apiFilter.field_name,
    label: apiFilter.label,
    type: apiFilter.type,
    options: apiFilter.options.map(option => ({
      name: option.name,   // 保持 name 字段
      value: String(option.value)  // 转换为字符串类型
    })),
    is_form: apiFilter.is_form,  // 是否在表单中显示
    is_search: apiFilter.is_search  // 是否在搜索中显示
  }))
})

// 筛选器变化处理
const handleFilterChange = (filters: Record<string, any>) => {
  currentFilters.value = filters

  // 重置页码并重新加载数据
  currentPage.value = 1
  loadDirectoryContents('筛选器变化')
}

// 处理搜索
const handleSearch = (query: string) => {
  searchQuery.value = query
  // 重置页码并重新加载数据
  currentPage.value = 1
  loadDirectoryContents('搜索查询')
}

// 处理刷新
const handleRefresh = () => {
  console.log('刷新文件列表')
  loadDirectoryContents('手动刷新')
}

// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadDirectoryContents('分页变化')
}

// 处理页大小变化
const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadDirectoryContents('页大小变化')
}

// 处理新建文件夹
const handleCreateFolder = () => {
  console.log('新建文件夹')
  // 这里可以添加新建文件夹逻辑
}

// 上传弹窗配置 
const uploadConfig = computed(() => {
  // 转换API筛选选项为上传属性选择器格式
  const attributeSelectors: AttributeSelector[] = apiFilterOptions.value.map(filter => ({
    field_name: filter.field_name,
    label: filter.label,
    type: filter.type,
    upload_type: filter.upload_type,
    placeholder: `请选择${filter.label}`,
    required: false,
    options: filter.options.map(option => ({
      name: option.name,
      value: option.value
    })),
    is_form: filter.is_form
  }))

  // 添加系统参数的隐藏选择器（用于自动设置值）
  const systemAttributeSelectors: AttributeSelector[] = [
    {
      field_name: 'category_id',
      label: '分类ID',
      type: 'select',
      upload_type: 'select',
      placeholder: '自动获取',
      required: true,
      options: [{ name: '当前分类', value: categoryId.value?.toString() || '' }],
      is_form: 'N' // 隐藏，自动设置
    },
    {
      field_name: 'parent_id',
      label: '父目录ID',
      type: 'select',
      upload_type: 'select',
      placeholder: '自动获取',
      required: true,
      options: [{ name: '当前目录', value: getCurrentParentId().toString() }],
      is_form: 'N' // 隐藏，自动设置
    }
  ]

  return {
    title: `上传${folderConfig.value.fileType}`,
    description: `选择要上传的${folderConfig.value.fileType}文件，支持拖拽上传`,
    attributeSelectors: [...systemAttributeSelectors, ...attributeSelectors],
    accept: '*',
    maxFiles: Number.MAX_SAFE_INTEGER, // 不做限制
    maxSize: Number.MAX_SAFE_INTEGER, // 不做限制 (约 8PB)
    // 自动设置的系统参数
    systemAttributes: {
      category_id: categoryId.value?.toString() || '',
      parent_id: getCurrentParentId().toString()
    }
  }
})

// 处理上传
const handleUpload = () => {
  uploadDialogOpen.value = true
}

// 处理拖拽上传 - 自动打开上传弹窗并预填充文件
const handleDragUpload = (files: File[]) => {
  console.log(`📁 拖拽上传: 接收到 ${files.length} 个文件，自动打开上传弹窗`)

  // 打开上传弹窗
  uploadDialogOpen.value = true

  // 这里可以添加预填充文件的逻辑
  // 由于 UploadDialog 组件会在打开时重置状态，
  // 我们需要在下一个 tick 中设置文件
  nextTick(() => {
    // 可以通过 ref 访问 UploadDialog 组件并设置文件
    // 或者通过 props 传递预设文件
    console.log('上传弹窗已打开，准备预填充文件')
  })
}

// 上传确认
const handleUploadConfirm = (data: UploadData) => {
  console.log('上传数据:', data)
  // 这里处理上传逻辑
}

// 上传取消
const handleUploadCancel = () => {
  console.log('取消上传')
}

// 上传成功
const handleUploadSuccess = () => {
  // 刷新列表
  loadDirectoryContents('上传成功')
}

// 上传失败
const handleUploadError = () => {
  // 错误处理已在上传组件中处理
}

// 所有文件上传完成后刷新文件列表
const handleFilesUploaded = () => {
  loadDirectoryContents('文件上传完成')
}

// 空状态显示文案
const emptyTitle = computed(() => {
  if (searchQuery.value.trim()) {
    return '没有找到匹配的文件'
  }
  return `暂无${folderConfig.value.fileType}`
})

// FilesView 组件引用
const filesViewRef = ref()

// 转换目录项为显示项
const convertToDisplayItem = (item: DirectoryItem): ItemType => {
  if (item.type === 'folder') {
    const folderItem = item as FolderItem
    // 传递文件夹的所有属性
    return {
      ...folderItem, // 直接展开所有属性
      type: 'folder' as const,
    }
  } else {
    const fileItem = item as FileItem
    // 传递文件的所有属性
    return {
      ...fileItem, // 直接展开所有属性
    }
  }
}

// 显示项列表 - 后端已筛选和排序，直接使用
const displayItems = computed(() => {
  return directoryData.value.items.map(convertToDisplayItem)
})

// 处理文件夹点击 - 导航到子目录
const handleFolderClick = (item: ItemType) => {
  if (item.type === 'folder') {
    const encodedFolderName = encodeURIComponent(item.name)
    const newPath = currentPath.value
      ? `${currentPath.value}/${encodedFolderName}`
      : encodedFolderName

    // 保持 categoryId 查询参数，并添加 parentId（被点击文件夹的 id）
    const query: Record<string, string> = {}
    if (categoryId.value) {
      query.categoryId = categoryId.value.toString()
    }
    // 将被点击文件夹的 id 作为 parentId 传递
    query.parentId = item.id

    router.push({
      path: `/resources/${folderType.value}/${newPath}`,
      query
    })
  }
}

// 处理文件点击 - 单击选择，双击预览
const handleFileClick = (item: ItemType) => {
  console.log('文件单击:', item.name)
  // 单击时可以添加选择逻辑或其他操作
}

// 处理文件双击 - 预览文件
const handleFileDoubleClick = (item: ItemType) => {
  // 检查是否支持预览 - 只需要检查是否有preview字段
  if (isSupportedFileType(item.preview)) {
    currentPreviewFile.value = item
    showPreview.value = true
  } else {
    toast.error('文件不支持预览')
  }
}

// 关闭预览
const closePreview = () => {
  showPreview.value = false
  currentPreviewFile.value = null
}

// 处理项目重命名
const handleItemRenamed = (_itemId: string, _newName: string, _item: ItemType) => {
  // 重命名成功后刷新目录
  handleRefreshDirectory()
}

// 监听 categoryId 变化，重置筛选状态并重新加载
watch(categoryId, async (newCategoryId, oldCategoryId) => {
  // 只有在组件已初始化且分类ID确实发生变化时才处理
  if (isInitialized.value && newCategoryId !== oldCategoryId && oldCategoryId !== null) {
    // 重置筛选状态
    currentFilters.value = {}
    searchQuery.value = ''
    currentPage.value = 1

    // 重新加载目录内容
    await loadDirectoryContents('切换分类')
  }
})

// 监听路由变化，重新加载数据
watch([folderType, currentPath], (newValues, oldValues) => {
  currentPage.value = 1 // 重置页码

  // 首次加载或路由真正发生变化时才加载
  if (!oldValues || JSON.stringify(newValues) !== JSON.stringify(oldValues)) {
    loadDirectoryContents(oldValues ? '路由变化' : '初始化加载')
  }
}, { immediate: true })

// 移除了重复的防抖处理，现在由ToolsBar组件统一处理防抖

// 下载功能集成 - 使用响应式配置
const downloadConfig = computed(() => ({
  category_id: categoryId.value?.toString() || '',
  savePath: undefined // 让用户选择保存路径
}))

const {
  downloadItem,
  downloadMultipleItems,
  isDownloading,
  showPathDialog,
  downloadInfo,
  handlePathConfirm,
  handlePathCancel
} = useFileDownload(downloadConfig)

// 文件操作处理
const handleFileAction = async (action: { action: string; item: ItemType }) => {
  const { action: actionType, item } = action

  switch (actionType) {
    case 'download':
      try {
        await downloadItem(item)
      } catch (error) {
        console.error('下载失败:', error)
      }
      break
  }
}

// 批量操作处理
const handleBatchAction = async (action: { action: string; items: ItemType[] }) => {
  const { action: actionType, items } = action

  switch (actionType) {
    case 'download':
      try {
        await downloadMultipleItems(items)
      } catch (error) {
        console.error('批量下载失败:', error)
      }
      break
  }
}

// 刷新目录
const handleRefreshDirectory = () => {
  console.log('刷新目录')
  loadDirectoryContents('刷新目录')
}


</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
  transition: background-color 0.2s;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--border) / 0.8);
}

/* 确保滚动区域有足够的内边距 */
.overflow-y-auto {
  padding-bottom: 1rem;
}
</style>