<template>
  <div class="flex flex-col min-h-full max-h-full">
    <!-- 主文件区域 -->
    <div ref="fileAreaRef" class="flex overflow-y-auto overflow-x-hidden relative flex-1 py-1 min-w-0 file-area" :class="[
      isDetailPanelVisible ? 'pr-2 mr-2' : '',
      isDragging ? 'drag-over' : ''
    ]" @dragenter="handleDragEnter" @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop">

      <!-- 拖拽上传遮罩 -->
      <div v-if="isDragging"
        class="flex absolute inset-0 z-50 justify-center items-center rounded-lg border-2 border-dashed bg-primary/10 border-primary">
        <div class="text-center">
          <Upload class="mx-auto mb-2 w-12 h-12 text-primary" />
          <p class="text-lg font-medium text-primary">拖拽文件到此处上传</p>
          <p class="text-sm text-muted-foreground">支持大量文件智能打包上传</p>
        </div>
      </div>

      <!-- 文件显示区域 -->
      <div class="flex flex-col flex-1 min-w-0">
        <div class="flex-1" v-if="paginatedItems.length > 0">
          <!-- 网格视图 -->
          <GridView v-if="viewMode === 'grid'" ref="gridViewRef" :items="paginatedItems" :show-checkboxes="true"
            :show-header="true" :selected-items="selectedIds" :is-downloading="isDownloading"
            @file-click="handleFileClick" @file-double-click="handleFileDoubleClick" @folder-click="handleFolderClick"
            @folder-double-click="handleFolderDoubleClick" @contextmenu="handleContextMenu"
            @selection-change="handleSelectionChange" @download="handleDownload" @rename="handleRename"
            @refresh-directory="handleRefreshDirectory" />

          <!-- 列表视图 -->
          <ListView v-else ref="listViewRef" :items="paginatedItems" :selected-items="selectedIds"
            :is-downloading="isDownloading" @file-click="handleFileClick" @file-double-click="handleFileDoubleClick"
            @folder-click="handleFolderClick" @folder-double-click="handleFolderDoubleClick"
            @contextmenu="handleContextMenu" @selection-change="handleSelectionChange" @download="handleDownload"
            @rename="handleRename" @refresh-directory="handleRefreshDirectory" />
        </div>

        <!-- 分页组件 -->
        <FilePagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total-items="totalItems"
          @page-change="handlePageChange" @page-size-change="handlePageSizeChange" />

        <!-- 空状态 -->
        <div v-if="totalItems === 0" class="flex-1 py-12 text-center">
          <component :is="emptyIcon" class="mx-auto mb-4 w-16 h-16 text-muted-foreground/50" />
          <h3 class="mb-2 text-lg font-medium text-muted-foreground">{{ emptyTitle }}</h3>
          <p class="mb-4 text-sm text-muted-foreground">{{ emptyMessage }}</p>
          <button
            class="inline-flex items-center px-4 py-2 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90"
            @click="handleUploadClick">
            <Upload class="mr-2 w-4 h-4" />
            {{ uploadButtonText }}
          </button>
        </div>

        <!-- 右键菜单 -->
        <ContextMenu :show="contextMenu.show" :x="contextMenu.x" :y="contextMenu.y" :item="contextMenu.item"
          :selected-items="getSelectedItems()" @close="hideContextMenu" @open-folder="openFolder"
          @download="handleContextDownload" @batch-download="handleContextBatchDownload" @rename="startRenameItem"
          @share="shareItem" @refresh-directory="handleRefreshDirectory" />
      </div>

      <!-- 详细信息面板 -->
      <Transition name="detail-panel-container" enter-active-class="detail-panel-container-enter-active"
        leave-active-class="detail-panel-container-leave-active" enter-from-class="detail-panel-container-enter-from"
        leave-to-class="detail-panel-container-leave-to">
        <div v-if="isDetailPanelVisible" class="py-1 w-80 min-h-full">
          <DetailPanel :selected-item="selectedItem" :custom-properties="computedCustomProperties || undefined"
            :is-editing="isEditing" :editing-properties="editingProperties" :is-saving="isSaving"
            :editable-properties="editableProperties" :start-editing="startEditing" :cancel-editing="cancelEditing"
            :save-editing="saveEditing" :update-editing-property="updateEditingProperty"
            :format-property-value="formatPropertyValue" :get-value-class="getValueClass" @close="hideDetailPanel" />
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Upload } from 'lucide-vue-next'
import GridView from './GridView.vue'
import ListView from './ListView.vue'
import DetailPanel from '../DetailPanel/index.vue'
import ContextMenu from './ContextMenu.vue'
import FilePagination from './Pagination.vue'
import { useRectangleSelection } from '@/composables/useRectangleSelection'
import { useCustomProperties, type ApiFilterGroup } from '../DetailPanel/useCustomProperties'
import { useBaseRename } from './useRename'
import { useDragAndDrop, type DragDropResult } from '@/composables/useDragAndDrop'
import { toast } from 'vue-sonner'

import type { ItemType, FileItemType, FolderItemType } from '@/types/files'

// Props
const props = withDefaults(defineProps<{
  items: ItemType[]
  viewMode: 'grid' | 'list'
  emptyIcon?: any
  emptyTitle?: string
  emptyMessage?: string
  uploadButtonText?: string
  apiFilterOptions?: ApiFilterGroup[]
  pageSize: number
  isDownloading?: boolean
}>(), {
  isDownloading: false,
  apiFilterOptions: () => []
})

// Emits
const emit = defineEmits<{
  fileClick: [item: FileItemType]
  fileDoubleClick: [item: FileItemType]
  folderClick: [item: FolderItemType]
  upload: []
  dragUpload: [files: File[]]
  itemRenamed: [itemId: string, newName: string, item: ItemType]
  itemAction: [itemAction: { action: string, item: ItemType }]
  batchAction: [batchAction: { action: string, items: ItemType[] }]
  deleteSuccess: []
  refreshDirectory: []
}>()

// 路由
const router = useRouter()
const route = useRoute()

// 重命名功能
const { executeRename } = useBaseRename(route.query.categoryId as string)

// 分页状态
const currentPage = ref(1)
const pageSize = ref(props.pageSize)

// 详细信息面板状态
const isDetailPanelVisible = ref(false)
const selectedItem = ref<ItemType | null>(null)

// 多选状态
const selectedIds = ref<Set<string>>(new Set())

// 主文件区域引用
const fileAreaRef = ref<HTMLElement | null>(null)
const gridViewRef = ref<InstanceType<typeof GridView> | null>(null)
const listViewRef = ref<InstanceType<typeof ListView> | null>(null)

// 右键菜单
const contextMenu = reactive({
  show: false,
  x: 0,
  y: 0,
  item: null as ItemType | null
})

// 计算属性
const displayItems = computed(() => props.items)

// 分页相关计算属性
const totalItems = computed(() => displayItems.value.length)

const totalPages = computed(() => {
  return Math.ceil(totalItems.value / pageSize.value)
})

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return displayItems.value.slice(start, end)
})

// 分页事件处理
const handlePageChange = (page: number) => {
  currentPage.value = page
  selectedIds.value.clear()
}

const handlePageSizeChange = (size: number) => {
  pageSize.value = size

  // 调整当前页，确保不超出范围
  const maxPage = Math.ceil(totalItems.value / size)
  if (currentPage.value > maxPage) {
    currentPage.value = Math.max(1, maxPage)
  }

  // 清空选中状态
  selectedIds.value.clear()


}

// 监听数据变化，重置分页
watch(() => props.items, () => {
  // 当数据源变化时，重置到第一页
  currentPage.value = 1
  selectedIds.value.clear()
}, { flush: 'sync' })

// 详细信息面板控制
const showDetailPanelFn = (item: ItemType) => {
  selectedItem.value = {
    ...item,
    createdAt: new Date(item.modifiedAt.getTime() - 24 * 60 * 60 * 1000), // 模拟创建时间
    path: item.type === 'folder' ? (item as any).path : undefined
  }
  isDetailPanelVisible.value = true
}

const hideDetailPanel = () => {
  isDetailPanelVisible.value = false
  selectedItem.value = null
}

// 使用自定义属性 composable
const {
  customProperties: computedCustomProperties,
  isEditing,
  editingProperties,
  isSaving,
  editableProperties,
  startEditing,
  cancelEditing,
  saveEditing,
  updateEditingProperty,
  formatPropertyValue,
  getValueClass
} = useCustomProperties(
  computed(() => selectedItem.value),
  computed(() => props.apiFilterOptions)
)

// 拖拽上传功能 - 使用统一的拖拽处理 composable
const {
  isDragging,
  handleDragEnter,
  handleDragOver,
  handleDragLeave,
  handleDrop
} = useDragAndDrop(
  {
    allowDirectories: true,
    maxFiles: 1000,
    useElectronAPI: true
  },
  {
    onFilesProcessed: (result: DragDropResult) => {
      if (result.files.length > 0) {
        // 转换为 File[] 格式
        const files = result.files.map(f => f.file)

        // 触发拖拽上传事件，让父组件打开上传弹窗
        emit('dragUpload', files)
      }
    },
    onError: (errors: string[]) => {
      errors.forEach(error => toast.error(error))
    },
    onSmartPackTriggered: async (files: File[], fileCount: number) => {
      // 文件数量达到阈值时自动启用智能打包
      console.log(`📦 自动启用智能打包: ${fileCount} 个文件`)
      toast.info(`检测到大量文件 (${fileCount} 个)，自动启用智能打包上传`, {
        duration: 5000,
      })

      try {
        // 获取路由信息
        const categoryId = route.query.categoryId as string
        const parentId = route.query.parentId as string || '0'

        // 检查是否在 Electron 环境
        if (typeof window !== 'undefined' && window.electronAPI?.tus) {
          // 提取文件路径（如果可用）
          const filePaths = files.map(file => (file as any).path).filter(Boolean)

          if (filePaths.length > 0) {
            // 使用 Electron 原生智能打包 API
            const smartPackResponse = await window.electronAPI.tus.smartPackUpload(filePaths, {
              threshold: 50,
              archiveName: `batch_upload_${Date.now()}`,
              metadata: {
                category_id: categoryId,
                parent_id: parentId,
                uploadSource: 'drag-drop-smart-pack'
              }
            })

            if (smartPackResponse.success) {
              toast.success(`智能打包上传已启动: ${fileCount} 个文件`)
              console.log(`📦 智能打包上传成功，任务ID: ${smartPackResponse.taskId}`)

              // 仍然触发 dragUpload 事件以打开上传弹窗显示进度
              emit('dragUpload', files)

              // 3秒后刷新文件列表
              setTimeout(() => {
                handleRefreshDirectory()
              }, 3000)
            } else {
              throw new Error(smartPackResponse.error || '智能打包上传失败')
            }
          } else {
            // 没有文件路径，降级到常规上传
            console.log('📦 无文件路径信息，降级到常规上传')
            emit('dragUpload', files)
          }
        } else {
          // 非 Electron 环境，降级到常规上传
          console.log('📦 非 Electron 环境，降级到常规上传')
          emit('dragUpload', files)
        }
      } catch (error) {
        console.error('智能打包上传失败:', error)
        toast.error(`智能打包上传失败: ${error}，将使用常规上传`)
        // 失败时降级到常规上传
        emit('dragUpload', files)
      }
    }
  }
)

// 路由信息现在由父组件处理，不再需要在这里获取

// 事件处理
const handleFileClick = (item: FileItemType) => {
  console.log('打开文件:', item.name)
  showDetailPanelFn(item)
  emit('fileClick', item)
}

const handleFileDoubleClick = (item: FileItemType) => {
  console.log('双击文件:', item.name)
  emit('fileDoubleClick', item)
}

const handleFolderClick = (item: FolderItemType) => {
  console.log('选择文件夹:', item.name)
  // 单击文件夹只显示详情，不进入
  showDetailPanelFn(item)
}

const handleFolderDoubleClick = (item: FolderItemType) => {
  console.log('进入文件夹:', item.name)

  // 双击文件夹才进入
  if (item.path) {
    router.push(item.path)
  } else {
    // 否则触发事件让父组件处理
    emit('folderClick', item)
  }
}

const handleContextMenu = (event: MouseEvent, item: ItemType) => {
  contextMenu.show = true
  contextMenu.x = event.clientX
  contextMenu.y = event.clientY
  contextMenu.item = item
}

// 重命名处理
const handleRename = async (itemId: string, newName: string, item: ItemType) => {
  // 调用重命名API - 传递原始项目、新名称和现有项目列表
  const success = await executeRename(item, newName, displayItems.value)

  if (success) {
    // 更新本地数据
    const targetItem = displayItems.value.find(item => item.id === itemId)
    if (targetItem) {
      targetItem.name = newName.trim()

      // 触发事件通知父组件
      emit('itemRenamed', itemId, newName.trim(), item)

      // 如果当前选中的是被重命名的项目，更新详情面板
      if (selectedItem.value && selectedItem.value.id === itemId) {
        selectedItem.value.name = newName.trim()
      }
    }
  }
}

const startRenameItem = (item: ItemType) => {
  hideContextMenu()

  // 根据当前视图模式调用对应的重命名方法
  if (props.viewMode === 'grid' && gridViewRef.value) {
    gridViewRef.value.startRename(item.id)
  } else if (props.viewMode === 'list' && listViewRef.value) {
    listViewRef.value.startRename(item.id)
  }
}

// 框选功能
const { setSelectedItems } = useRectangleSelection({
  containerRef: fileAreaRef,
  itemSelector: '[data-id]',
  getItemId: (element) => {
    return element.dataset.id || null
  },
  onSelectionChange: (newSelectedIds) => {
    selectedIds.value = newSelectedIds
  }
})

const handleSelectionChange = (newSelectedIds: Set<string>) => {
  selectedIds.value = newSelectedIds
  setSelectedItems(newSelectedIds)
}

// 处理下载操作
const handleDownload = () => {
  if (selectedIds.value.size === 0) return

  if (selectedIds.value.size === 1) {
    // 单个项目下载
    const itemId = Array.from(selectedIds.value)[0]
    const item = displayItems.value.find(item => item.id === itemId)
    if (item) {
      emit('itemAction', { action: 'download', item })
    }
  } else {
    // 批量下载
    const selectedItems = displayItems.value.filter(item => selectedIds.value.has(item.id))
    emit('batchAction', { action: 'download', items: selectedItems })
  }

  // 清除选择
  selectedIds.value.clear()
}

// 处理上下文菜单下载
const handleContextDownload = (item: ItemType) => {
  emit('itemAction', { action: 'download', item })
}

// 处理上下文菜单批量下载
const handleContextBatchDownload = (items: ItemType[]) => {
  emit('batchAction', { action: 'download', items })
}

// 获取当前选中的文件列表
const getSelectedItems = (): ItemType[] => {
  return displayItems.value.filter(item => selectedIds.value.has(item.id))
}

const handleUploadClick = () => {
  emit('upload')
}

const hideContextMenu = () => {
  contextMenu.show = false
  contextMenu.item = null
}

// 右键菜单操作函数
const openFolder = (item: FolderItemType) => {
  handleFolderDoubleClick(item)
  hideContextMenu()
}

const shareItem = (item: ItemType) => {
  console.log('分享:', item.name)
  hideContextMenu()
}

const handleRefreshDirectory = () => {
  emit('refreshDirectory')
}

// 新的拖拽处理逻辑已在上面的 useDragAndDrop composable 中实现

// 旧的拖拽处理函数已移除，现在使用 useDragAndDrop composable

// 生命周期
onMounted(() => {
  document.addEventListener('click', hideContextMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', hideContextMenu)
})

// 暴露一些方法给父组件
defineExpose({
  // 分页控制
  getCurrentPage: () => currentPage.value,
  setCurrentPage: (page: number) => {
    currentPage.value = Math.max(1, Math.min(page, totalPages.value))
  },
  getPageSize: () => pageSize.value,
  setPageSize: (size: number) => {
    handlePageSizeChange(size)
  },
  getTotalPages: () => totalPages.value,
  getTotalItems: () => totalItems.value,

  // 选中状态
  getSelectedItems: () => Array.from(selectedIds.value),
  clearSelection: () => selectedIds.value.clear(),

  // 详情面板
  showDetailPanel: showDetailPanelFn,
  hideDetailPanel,
})
</script>

<style scoped>
/* 详细信息面板容器动画 */
.detail-panel-container-enter-active,
.detail-panel-container-leave-active {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-panel-container-enter-from {
  width: 0;
  opacity: 0;
  transform: translateX(50px);
}

.detail-panel-container-leave-to {
  width: 0;
  opacity: 0;
  transform: translateX(50px);
}

/* 优化动画性能 */
.detail-panel-container-enter-active,
.detail-panel-container-leave-active {
  will-change: width, opacity, transform;
  overflow: hidden;
}

/* 文件区域框选样式 */
.file-area {
  position: relative;
  user-select: none;
}

/* 框选时禁用所有过渡效果，避免干扰 */
.file-area:has(.rectangle-selection) * {
  transition: none !important;
}

/* 拖拽上传样式 */
.file-area.drag-over {
  background-color: rgba(var(--primary), 0.05);
}

.file-area {
  transition: background-color 0.2s ease;
}
</style>