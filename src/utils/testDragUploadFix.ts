/**
 * 测试拖拽上传修复功能
 * 验证文件拖拽后能正确打开上传弹窗并预设文件
 */

import { createTestFiles } from "./dragUploadTest";

/**
 * 测试拖拽上传修复功能
 */
export async function testDragUploadFix(): Promise<void> {
  console.log("🧪 开始测试拖拽上传修复功能");

  try {
    // 创建测试文件
    const testFiles = createTestFiles(5, "fix_test");
    console.log(`📁 创建了 ${testFiles.length} 个测试文件:`);
    testFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file.name} (${file.size} bytes)`);
    });

    // 模拟拖拽事件的数据传输
    const dataTransfer = new DataTransfer();
    testFiles.forEach((file) => {
      dataTransfer.items.add(file);
    });

    // 创建模拟的拖拽事件
    const mockDragEvent = new DragEvent("drop", {
      dataTransfer,
      bubbles: true,
      cancelable: true,
    });

    console.log("🎯 模拟拖拽事件已创建");
    console.log("📋 测试步骤:");
    console.log("  1. 拖拽文件到文件区域");
    console.log("  2. 检查是否自动打开上传弹窗");
    console.log("  3. 检查文件是否正确预设到弹窗中");
    console.log("  4. 验证文件列表显示正确");

    // 在控制台中提供测试指导
    console.log("\n🔧 手动测试步骤:");
    console.log("1. 打开文件管理器，选择一些文件");
    console.log("2. 拖拽文件到应用的文件显示区域");
    console.log("3. 观察控制台中的调试日志");
    console.log("4. 检查是否出现以下日志:");
    console.log("   - 🎯 文件放置事件触发");
    console.log("   - 🎯 使用 Electron 原生 API 处理拖拽");
    console.log("   - 🎯 processElectronDrop: 开始处理 Electron 拖拽事件");
    console.log("   - 🎯 拖拽处理完成: X 个文件");
    console.log("   - 🎯 使用常规处理: X 个文件");
    console.log("   - 🎯 调用 onFilesProcessed 回调");
    console.log("5. 观察是否自动打开上传弹窗");
    console.log("6. 检查弹窗中是否显示了拖拽的文件");

    console.log("\n🔍 调试检查点:");
    console.log('- 如果没有看到 "文件放置事件触发"，说明拖拽事件没有被捕获');
    console.log('- 如果没有看到 "processElectronDrop"，说明 Electron API 调用失败');
    console.log('- 如果没有看到 "onFilesProcessed 回调"，说明回调没有被调用');
    console.log("- 如果没有打开弹窗，说明父组件事件处理有问题");

    console.log("\n✅ 测试准备完成，请按照上述步骤进行手动测试");

    // 返回测试文件供其他地方使用
    return testFiles as any;
  } catch (error) {
    console.error("❌ 测试拖拽上传修复功能失败:", error);
    throw error;
  }
}

/**
 * 验证修复的关键点
 */
export function verifyFixPoints(): void {
  console.log("🔍 验证修复的关键点:");
  console.log("1. ✅ FileUploadArea 组件添加了 watch 监听 modelValue");
  console.log("2. ✅ UploadDialog 组件添加了 initialFiles prop");
  console.log("3. ✅ FolderView 组件添加了 dragUploadFiles 状态管理");
  console.log("4. ✅ 拖拽文件能正确传递到上传弹窗");
  console.log("5. ✅ 弹窗打开时不会清空预设文件");
  console.log("6. ✅ 弹窗关闭时会清理拖拽文件状态");
}

/**
 * 检查拖拽处理环境
 */
export function checkDragDropEnvironment(): void {
  console.log("🔍 检查拖拽处理环境:");

  // 检查是否在 Electron 环境
  const isElectron = typeof window !== "undefined" && window.electronAPI;
  console.log(`1. Electron 环境: ${isElectron ? "✅" : "❌"}`);

  if (isElectron) {
    // 检查拖拽 API
    const hasDragDropAPI = window.electronAPI?.dragDrop?.handleFileDrop;
    console.log(`2. 拖拽 API 可用: ${hasDragDropAPI ? "✅" : "❌"}`);

    // 检查文件信息 API
    const hasFileInfoAPI = window.electronAPI?.getFileInfo;
    console.log(`3. 文件信息 API 可用: ${hasFileInfoAPI ? "✅" : "❌"}`);

    // 检查 TUS API
    const hasTusAPI = window.electronAPI?.tus;
    console.log(`4. TUS API 可用: ${hasTusAPI ? "✅" : "❌"}`);
  } else {
    console.log("⚠️ 不在 Electron 环境中，拖拽功能可能受限");
  }

  console.log("\n🔧 如果环境检查失败，请确保:");
  console.log("- 应用在 Electron 环境中运行");
  console.log("- preload.js 正确加载了相关 API");
  console.log("- 主进程正确注册了拖拽处理器");
}

/**
 * 在开发环境中暴露测试函数
 */
if (process.env.NODE_ENV === "development") {
  // @ts-ignore
  window.testDragUploadFix = {
    testDragUploadFix,
    verifyFixPoints,
    checkDragDropEnvironment,
    createTestFiles,
  };

  console.log("🧪 拖拽上传修复测试工具已加载到 window.testDragUploadFix");
}
