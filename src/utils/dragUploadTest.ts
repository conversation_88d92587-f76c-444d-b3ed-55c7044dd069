/**
 * 拖拽上传功能测试工具
 * 用于验证智能打包功能和拖拽处理逻辑
 */

import { toast } from "vue-sonner";

/**
 * 创建测试文件对象
 */
export function createTestFiles(count: number, prefix: string = "test"): File[] {
  const files: File[] = [];

  for (let i = 0; i < count; i++) {
    const fileName = `${prefix}_file_${i + 1}.txt`;
    const content = `This is test file ${i + 1}\nCreated for drag upload testing\nFile size: ${Math.random() * 1000} bytes`;

    const file = new File([content], fileName, {
      type: "text/plain",
      lastModified: Date.now(),
    });

    // 模拟文件夹结构
    if (count > 10) {
      const folderName = `folder_${Math.floor(i / 10) + 1}`;
      Object.defineProperty(file, "webkitRelativePath", {
        value: `${folderName}/${fileName}`,
        writable: false,
      });
    }

    files.push(file);
  }

  return files;
}

/**
 * 测试智能打包自动触发功能
 */
export function testSmartPackingAutoTrigger(fileCount: number): void {
  console.log(`🧪 测试智能打包自动触发: ${fileCount} 个文件`);

  if (fileCount >= 50) {
    toast.info(`检测到大量文件 (${fileCount} 个)，自动启用智能打包上传`, {
      duration: 5000,
    });
    console.log(`✅ 智能打包已自动触发`);
  } else {
    console.log(`ℹ️ 文件数量 ${fileCount} 未达到智能打包阈值 (50)，使用常规上传`);
  }
}

/**
 * 模拟拖拽事件
 */
export function createMockDragEvent(files: File[]): DragEvent {
  const dataTransfer = new DataTransfer();

  files.forEach((file) => {
    dataTransfer.items.add(file);
  });

  const dragEvent = new DragEvent("drop", {
    dataTransfer,
    bubbles: true,
    cancelable: true,
  });

  return dragEvent;
}

/**
 * 测试拖拽上传流程
 */
export async function testDragUploadFlow(fileCount: number): Promise<void> {
  console.log(`🧪 开始测试拖拽上传流程: ${fileCount} 个文件`);

  try {
    // 创建测试文件
    const files = createTestFiles(fileCount, "drag_test");
    console.log(`📁 创建了 ${files.length} 个测试文件`);

    // 测试智能打包自动触发
    testSmartPackingAutoTrigger(fileCount);

    // 模拟拖拽事件（暂时不使用）
    // const dragEvent = createMockDragEvent(files);
    console.log(`🎯 模拟拖拽事件创建完成`);

    // 显示测试结果
    toast.success(`拖拽上传测试完成: ${fileCount} 个文件`);

    console.log(`✅ 拖拽上传流程测试完成`);
  } catch (error) {
    console.error(`❌ 拖拽上传测试失败:`, error);
    toast.error(`拖拽上传测试失败: ${error}`);
  }
}

/**
 * 测试场景配置
 */
export const testScenarios = {
  // 少量文件测试
  smallFiles: {
    name: "少量文件拖拽",
    fileCount: 10,
    description: "测试少量文件的拖拽上传，不应触发智能打包建议",
  },

  // 智能打包阈值测试
  smartPackThreshold: {
    name: "智能打包阈值测试",
    fileCount: 50,
    description: "测试达到智能打包阈值的文件拖拽，应自动启用智能打包",
  },

  // 大量文件测试
  largeFiles: {
    name: "大量文件拖拽",
    fileCount: 100,
    description: "测试大量文件的拖拽上传，应自动启用智能打包",
  },

  // 极大量文件测试
  massiveFiles: {
    name: "极大量文件拖拽",
    fileCount: 500,
    description: "测试极大量文件的拖拽上传，验证性能和智能打包功能",
  },
};

/**
 * 运行所有测试场景
 */
export async function runAllTests(): Promise<void> {
  console.log(`🧪 开始运行所有拖拽上传测试场景`);

  for (const [, scenario] of Object.entries(testScenarios)) {
    console.log(`\n📋 测试场景: ${scenario.name}`);
    console.log(`📝 描述: ${scenario.description}`);

    await testDragUploadFlow(scenario.fileCount);

    // 等待一段时间再进行下一个测试
    await new Promise((resolve) => setTimeout(resolve, 2000));
  }

  console.log(`\n✅ 所有测试场景完成`);
  toast.success("所有拖拽上传测试场景已完成");
}

/**
 * 验证智能打包功能是否可用
 */
export async function verifySmartPackingAvailability(): Promise<boolean> {
  try {
    // 检查是否在 Electron 环境
    if (typeof window === "undefined" || !window.electronAPI) {
      console.warn("⚠️ 不在 Electron 环境中，智能打包功能不可用");
      return false;
    }

    // 检查 TUS API 是否可用
    if (!window.electronAPI.tus) {
      console.warn("⚠️ TUS API 不可用，智能打包功能不可用");
      return false;
    }

    // 检查智能打包 API 是否可用
    if (!window.electronAPI.tus.analyzeSmartPacking) {
      console.warn("⚠️ 智能打包分析 API 不可用");
      return false;
    }

    console.log("✅ 智能打包功能可用");
    return true;
  } catch (error) {
    console.error("❌ 验证智能打包功能时出错:", error);
    return false;
  }
}

/**
 * 在开发环境中暴露测试函数到全局
 */
if (process.env.NODE_ENV === "development") {
  // @ts-ignore
  window.dragUploadTest = {
    createTestFiles,
    testSmartPackingAutoTrigger,
    testDragUploadFlow,
    runAllTests,
    verifySmartPackingAvailability,
    testScenarios,
  };

  console.log("🧪 拖拽上传测试工具已加载到 window.dragUploadTest");
}
