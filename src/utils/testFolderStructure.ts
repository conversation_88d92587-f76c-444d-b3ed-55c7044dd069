/**
 * 测试文件夹结构处理功能
 * 验证拖拽文件夹时是否正确保留层级结构
 */

/**
 * 检查文件的相对路径信息
 */
export function checkFileStructure(files: File[]): void {
  console.log('🔍 检查文件夹结构处理:')
  console.log(`总文件数: ${files.length}`)
  
  const filesWithRelativePath = files.filter(file => (file as any).webkitRelativePath)
  const filesWithPath = files.filter(file => (file as any).path)
  
  console.log(`带有 webkitRelativePath 的文件: ${filesWithRelativePath.length}`)
  console.log(`带有 path 的文件: ${filesWithPath.length}`)
  
  if (filesWithRelativePath.length > 0) {
    console.log('\n📁 文件夹结构信息:')
    
    // 分析文件夹结构
    const folderStructure: { [folder: string]: string[] } = {}
    
    filesWithRelativePath.forEach(file => {
      const relativePath = (file as any).webkitRelativePath as string
      const pathParts = relativePath.split('/')
      const rootFolder = pathParts[0]
      
      if (!folderStructure[rootFolder]) {
        folderStructure[rootFolder] = []
      }
      folderStructure[rootFolder].push(relativePath)
    })
    
    Object.entries(folderStructure).forEach(([folder, paths]) => {
      console.log(`📂 ${folder}/ (${paths.length} 个文件)`)
      paths.slice(0, 5).forEach(path => {
        console.log(`  └─ ${path}`)
      })
      if (paths.length > 5) {
        console.log(`  └─ ... 还有 ${paths.length - 5} 个文件`)
      }
    })
  }
  
  if (filesWithPath.length > 0) {
    console.log('\n🗂️ 文件路径信息:')
    filesWithPath.slice(0, 5).forEach(file => {
      const path = (file as any).path as string
      const relativePath = (file as any).webkitRelativePath as string
      console.log(`📄 ${file.name}`)
      console.log(`   路径: ${path}`)
      if (relativePath) {
        console.log(`   相对路径: ${relativePath}`)
      }
    })
    if (filesWithPath.length > 5) {
      console.log(`   ... 还有 ${filesWithPath.length - 5} 个文件`)
    }
  }
  
  // 验证结果
  console.log('\n✅ 验证结果:')
  if (filesWithRelativePath.length > 0) {
    console.log('✅ 检测到文件夹结构，相对路径信息已保留')
  } else {
    console.log('ℹ️ 未检测到文件夹结构（可能是单个文件拖拽）')
  }
  
  if (filesWithPath.length > 0) {
    console.log('✅ 文件路径信息已保留（Electron 环境）')
  } else {
    console.log('⚠️ 未检测到文件路径信息（可能不在 Electron 环境中）')
  }
}

/**
 * 验证文件夹结构修复的关键点
 */
export function verifyFolderStructureFix(): void {
  console.log('🔍 验证文件夹结构修复的关键点:')
  console.log('1. ✅ Electron 主进程正确计算相对路径')
  console.log('2. ✅ createFileFromPath 函数设置 webkitRelativePath')
  console.log('3. ✅ processElectronDrop 传递相对路径信息')
  console.log('4. ✅ useTusUpload 支持文件夹结构上传')
  console.log('5. ✅ 文件名使用相对路径保持层级结构')
  
  console.log('\n🧪 测试步骤:')
  console.log('1. 创建一个包含子文件夹的测试文件夹')
  console.log('2. 拖拽整个文件夹到上传区域')
  console.log('3. 在控制台运行: window.testFolderStructure.checkFileStructure(files)')
  console.log('4. 检查是否显示正确的文件夹层级结构')
  console.log('5. 开始上传，验证服务器端是否保持文件夹结构')
}

/**
 * 在开发环境中暴露测试函数
 */
if (process.env.NODE_ENV === 'development') {
  // @ts-ignore
  window.testFolderStructure = {
    checkFileStructure,
    verifyFolderStructureFix
  }

  console.log('🧪 文件夹结构测试工具已加载到 window.testFolderStructure')
}
