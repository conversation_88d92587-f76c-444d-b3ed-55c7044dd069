import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import router from "./router";
import App from "./App.vue";
import "./style.css";

const app = createApp(App);

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

app.use(pinia);
app.use(router);

// 导入测试工具（仅在开发环境）
if (process.env.NODE_ENV === "development") {
  import("./utils/testFolderStructure");
}

// 应用启动
app.mount("#app");
