<template>
  <div class="p-6 space-y-6">
    <div class="text-center">
      <h2 class="text-2xl font-bold mb-2">拖拽上传功能测试</h2>
      <p class="text-muted-foreground">测试统一拖拽处理逻辑和智能打包功能</p>
    </div>

    <!-- 测试按钮组 -->
    <div class="flex flex-wrap gap-3 justify-center">
      <Button @click="testSmallFiles" variant="outline">
        测试少量文件 (10个)
      </Button>
      <Button @click="testThresholdFiles" variant="outline">
        测试阈值文件 (50个)
      </Button>
      <Button @click="testLargeFiles" variant="outline">
        测试大量文件 (100个)
      </Button>
      <Button @click="testMassiveFiles" variant="outline">
        测试极大量文件 (500个)
      </Button>
      <Button @click="runAllTests" variant="default">
        运行所有测试
      </Button>
    </div>

    <!-- 拖拽测试区域 -->
    <div class="relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200" :class="{
      'border-primary bg-accent': isDragging,
      'border-border hover:border-primary hover:bg-accent': !isDragging
    }" @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop">
      <div v-if="isDragging" class="absolute inset-0 flex items-center justify-center bg-primary/10 rounded-lg">
        <div class="text-center">
          <Upload class="mx-auto mb-2 w-12 h-12 text-primary" />
          <p class="text-lg font-medium text-primary">拖拽文件到此处测试</p>
          <p class="text-sm text-muted-foreground">将显示智能打包建议和处理结果</p>
        </div>
      </div>

      <div v-else class="space-y-4">
        <Upload class="mx-auto w-16 h-16 text-muted-foreground" />
        <div>
          <h3 class="text-lg font-semibold mb-2">拖拽文件到此处测试</h3>
          <p class="text-muted-foreground">
            支持文件和文件夹拖拽，自动测试智能打包功能
          </p>
        </div>
      </div>
    </div>

    <!-- 测试结果显示 -->
    <div v-if="testResults.length > 0" class="space-y-3">
      <h3 class="text-lg font-semibold">测试结果</h3>
      <div class="space-y-2 max-h-60 overflow-y-auto">
        <div v-for="(result, index) in testResults" :key="index" class="p-3 rounded-lg border" :class="{
          'bg-green-50 border-green-200': result.type === 'success',
          'bg-blue-50 border-blue-200': result.type === 'info',
          'bg-red-50 border-red-200': result.type === 'error'
        }">
          <div class="flex items-start gap-2">
            <div class="flex-shrink-0 mt-0.5">
              <CheckCircle v-if="result.type === 'success'" class="w-4 h-4 text-green-600" />
              <Info v-else-if="result.type === 'info'" class="w-4 h-4 text-blue-600" />
              <AlertCircle v-else class="w-4 h-4 text-red-600" />
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium">{{ result.message }}</p>
              <p v-if="result.details" class="text-xs text-muted-foreground mt-1">
                {{ result.details }}
              </p>
            </div>
            <span class="text-xs text-muted-foreground">
              {{ formatTime(result.timestamp) }}
            </span>
          </div>
        </div>
      </div>
      <Button @click="clearResults" variant="outline" size="sm">
        清空结果
      </Button>
    </div>

    <!-- 功能状态检查 -->
    <div class="space-y-3">
      <h3 class="text-lg font-semibold">功能状态检查</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="p-4 rounded-lg border">
          <h4 class="font-medium mb-2">Electron API</h4>
          <div class="space-y-1 text-sm">
            <div class="flex justify-between">
              <span>Electron 环境:</span>
              <span :class="isElectron ? 'text-green-600' : 'text-red-600'">
                {{ isElectron ? '✓ 可用' : '✗ 不可用' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>拖拽 API:</span>
              <span :class="hasDragAPI ? 'text-green-600' : 'text-red-600'">
                {{ hasDragAPI ? '✓ 可用' : '✗ 不可用' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>智能打包 API:</span>
              <span :class="hasSmartPackAPI ? 'text-green-600' : 'text-red-600'">
                {{ hasSmartPackAPI ? '✓ 可用' : '✗ 不可用' }}
              </span>
            </div>
          </div>
        </div>

        <div class="p-4 rounded-lg border">
          <h4 class="font-medium mb-2">拖拽处理</h4>
          <div class="space-y-1 text-sm">
            <div class="flex justify-between">
              <span>统一 Composable:</span>
              <span class="text-green-600">✓ 已集成</span>
            </div>
            <div class="flex justify-between">
              <span>智能打包阈值:</span>
              <span class="text-blue-600">50 个文件</span>
            </div>
            <div class="flex justify-between">
              <span>错误处理:</span>
              <span class="text-green-600">✓ 已实现</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Upload, CheckCircle, Info, AlertCircle } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { useDragAndDrop, type DragDropResult } from '@/composables/useDragAndDrop'
import {
  createTestFiles,
  testSmartPackingAutoTrigger,
  verifySmartPackingAvailability,
  runAllTests as runAllTestScenarios
} from '@/utils/dragUploadTest'

interface TestResult {
  type: 'success' | 'info' | 'error'
  message: string
  details?: string
  timestamp: Date
}

const testResults = ref<TestResult[]>([])

// 使用统一拖拽处理
const {
  isDragging,
  handleDragOver,
  handleDragLeave,
  handleDrop: handleUnifiedDrop
} = useDragAndDrop(
  {
    allowDirectories: true,
    maxFiles: 1000,
    useElectronAPI: true
  },
  {
    onFilesProcessed: (result: DragDropResult) => {
      addTestResult('success', `成功处理 ${result.files.length} 个文件`,
        `来源: ${result.isElectronDrop ? 'Electron 原生' : '前端 API'}`)
    },
    onError: (errors: string[]) => {
      errors.forEach(error => {
        addTestResult('error', '处理失败', error)
      })
    },
    onSmartPackTriggered: async (_files: File[], fileCount: number) => {
      addTestResult('info', `智能打包已自动触发`, `文件数量: ${fileCount}`)
    }
  }
)

// 功能状态检查
const isElectron = computed(() => {
  return typeof window !== 'undefined' && !!window.electronAPI
})

const hasDragAPI = computed(() => {
  return isElectron.value && !!window.electronAPI?.dragDrop
})

const hasSmartPackAPI = computed(() => {
  return isElectron.value && !!window.electronAPI.tus?.analyzeSmartPacking
})

// 添加测试结果
const addTestResult = (type: TestResult['type'], message: string, details?: string) => {
  testResults.value.unshift({
    type,
    message,
    details,
    timestamp: new Date()
  })

  // 限制结果数量
  if (testResults.value.length > 50) {
    testResults.value = testResults.value.slice(0, 50)
  }
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString()
}

// 清空结果
const clearResults = () => {
  testResults.value = []
}

// 处理拖拽
const handleDrop = async (event: DragEvent) => {
  await handleUnifiedDrop(event)
}

// 测试函数
const testSmallFiles = () => {
  const files = createTestFiles(10, 'small_test')
  addTestResult('info', '开始测试少量文件', `创建了 ${files.length} 个测试文件`)
  testSmartPackingAutoTrigger(files.length)
}

const testThresholdFiles = () => {
  const files = createTestFiles(50, 'threshold_test')
  addTestResult('info', '开始测试阈值文件', `创建了 ${files.length} 个测试文件`)
  testSmartPackingAutoTrigger(files.length)
}

const testLargeFiles = () => {
  const files = createTestFiles(100, 'large_test')
  addTestResult('info', '开始测试大量文件', `创建了 ${files.length} 个测试文件`)
  testSmartPackingAutoTrigger(files.length)
}

const testMassiveFiles = () => {
  const files = createTestFiles(500, 'massive_test')
  addTestResult('info', '开始测试极大量文件', `创建了 ${files.length} 个测试文件`)
  testSmartPackingAutoTrigger(files.length)
}

const runAllTests = async () => {
  addTestResult('info', '开始运行所有测试场景', '这可能需要一些时间...')

  try {
    await runAllTestScenarios()
    addTestResult('success', '所有测试场景已完成', '请查看控制台获取详细信息')
  } catch (error) {
    addTestResult('error', '测试运行失败', String(error))
  }
}

// 组件挂载时检查功能状态
onMounted(async () => {
  addTestResult('info', '拖拽上传测试组件已加载')

  const isAvailable = await verifySmartPackingAvailability()
  if (isAvailable) {
    addTestResult('success', '智能打包功能验证通过')
  } else {
    addTestResult('error', '智能打包功能不可用', '请在 Electron 环境中测试')
  }
})
</script>
